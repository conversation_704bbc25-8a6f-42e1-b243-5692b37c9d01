<?php
/**
 * The template for displaying the footer
 *
 * Contains the closing of the #content div and all content after.
 *
 * @package Nols_ESPA_Theme_Two
 */
?>

</div><!-- #content -->

<?php if (is_active_sidebar('footer-widgets')) : ?>
    <div class="footer-widgets-area">
        <div class="footer-widgets-container">
            <?php dynamic_sidebar('footer-widgets'); ?>
        </div>
    </div>
<?php endif; ?>

<!-- Footer -->
<footer class="site-footer" id="colophon">
    <div class="footer-content">

        
        <div class="site-info">
            <p>
                <?php
                printf(
                    esc_html__('© %1$s %2$s. Celebrating Papua New Guinea Heritage.', 'nols-espa-theme-two'),
                    date('Y'),
                    get_bloginfo('name')
                );
                ?>
            </p>
            <p>
                <?php
                printf(
                    esc_html__('Theme: %1$s by %2$s | Website: %3$s | Email: %4$s', 'nols-espa-theme-two'),
                    '<strong>Nols ESPA Theme Two</strong>',
                    '<a href="https://www.dakoiims.com" target="_blank" rel="noopener">Noland Gande</a>',
                    '<a href="https://www.dakoiims.com" target="_blank" rel="noopener">www.dakoiims.com</a>',
                    '<a href="mailto:<EMAIL>"><EMAIL></a>'
                );
                ?>
            </p>
            <p>
                <?php esc_html_e('East Sepik Province, Papua New Guinea', 'nols-espa-theme-two'); ?>
            </p>
        </div>
        
        <?php if (function_exists('wp_privacy_policy_url') && wp_privacy_policy_url()) : ?>
            <div class="privacy-policy">
                <a href="<?php echo esc_url(wp_privacy_policy_url()); ?>">
                    <?php esc_html_e('Privacy Policy', 'nols-espa-theme-two'); ?>
                </a>
            </div>
        <?php endif; ?>
    </div>
</footer>

<a href="#" class="back-to-top" id="back-to-top" title="<?php esc_attr_e('Back to Top', 'nols-espa-theme-two'); ?>">
    <span class="screen-reader-text"><?php esc_html_e('Back to Top', 'nols-espa-theme-two'); ?></span>
    ▲
</a>

<?php wp_footer(); ?>

<script>
// Back to top functionality
document.addEventListener('DOMContentLoaded', function() {
    const backToTop = document.getElementById('back-to-top');
    
    if (backToTop) {
        // Show/hide back to top button
        window.addEventListener('scroll', function() {
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'block';
                backToTop.style.opacity = '1';
            } else {
                backToTop.style.opacity = '0';
                setTimeout(function() {
                    if (window.pageYOffset <= 300) {
                        backToTop.style.display = 'none';
                    }
                }, 300);
            }
        });
        
        // Smooth scroll to top
        backToTop.addEventListener('click', function(e) {
            e.preventDefault();
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
    

    
    // Add loading animation delay
    const animatedElements = document.querySelectorAll('.post, .widget, article');
    animatedElements.forEach((element, index) => {
        element.style.animationDelay = `${index * 0.1}s`;
    });
    
    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function (e) {
            const target = document.querySelector(this.getAttribute('href'));
            if (target) {
                e.preventDefault();
                target.scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }
        });
    });

    // Simple Mobile Menu Toggle
    const menuToggle = document.getElementById('menu-toggle');
    const navMenu = document.querySelector('.nav-menu');

    if (menuToggle && navMenu) {
        menuToggle.addEventListener('click', function() {
            // Force toggle - always add if not present, remove if present
            if (navMenu.classList.contains('is-open')) {
                navMenu.classList.remove('is-open');
                this.innerHTML = '☰';
            } else {
                navMenu.classList.add('is-open');
                this.innerHTML = '✕';
            }
        });
    }
});
</script>

</body>
</html>
